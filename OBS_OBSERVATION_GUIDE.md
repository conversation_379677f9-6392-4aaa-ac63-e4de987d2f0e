# 👁️ Guide d'observation des états OBS

Ce guide explique comment OBS Video Hub observe les états d'OBS Studio et comment utiliser ces informations pour créer des automations personnalisées.

## 🎯 Concept

OBS Video Hub agit comme un **observateur intelligent** des états OBS :
- **Écoute** les changements en temps réel (volume, mute, scènes, visibilité des sources)
- **Stocke** les états actuels en mémoire
- **Expose** ces informations via des API et événements
- **Permet** la création d'automations basées sur ces états

## 🏗️ Architecture modulaire

Le module OBS est maintenant organisé en **3 services spécialisés** :

### 📡 **OBSModuleService** (Service principal)
- **Responsabilité** : Connexion WebSocket et orchestration
- **Rôle** : Point d'entrée principal, gère la connexion OBS
- **Interface** : Implémente `IMicrophoneModule` pour la compatibilité

### 👁️ **OBSObserverService** (Observation)
- **Responsabilité** : Écouter et traiter les événements OBS
- **Rôle** : Observer les changements d'état en temps réel
- **États gérés** : Volume, mute, scènes, visibilité des sources

### 🎮 **OBSControllerService** (Contrôle)
- **Responsabilité** : Envoyer des commandes vers OBS
- **Rôle** : Actions de contrôle (actuellement : `changeScene`)
- **Extensible** : Facile d'ajouter de nouvelles commandes

## 📊 États observés

### 🔊 Sources audio
- **Volume** : Niveau en dB et multiplicateur
- **Mute** : État muet/actif de chaque source
- **Changements** : Détection en temps réel des modifications

### 🎬 Scènes et sources
- **Scène actuelle** : Quelle scène est en cours
- **Visibilité des sources** : Quelles sources sont visibles dans chaque scène
- **Changements de scène** : Détection des transitions

## 🔧 API d'observation

### Méthodes disponibles

Le module OBS expose ces méthodes pour consulter les états :

```typescript
// Sources audio
obsModule.getAllAudioLevels()           // Tous les niveaux de volume
obsModule.getMicState('MIC1')           // État d'une source spécifique
obsModule.getAllMicStates()             // États de toutes les sources

// Scènes
obsModule.getCurrentScene()             // Scène actuelle
obsModule.getAllScenes()                // Toutes les scènes et leurs états
obsModule.getSceneState('Scene1')       // État d'une scène spécifique

// Visibilité des sources
obsModule.isSourceVisibleInCurrentScene('MIC1')    // Source visible ?
obsModule.getVisibleSourcesInCurrentScene()        // Toutes les sources visibles
```

### Événements émis

Le module OBS émet ces événements que vous pouvez écouter :

```typescript
// Changements de volume
{
    type: 'volume_changed',
    data: {
        sourceName: 'MIC1',
        volumeDb: -12.5,
        volumeMul: 0.25,
        timestamp: Date
    }
}

// Changements de mute
{
    type: 'mic_state_changed',
    data: {
        micName: 'MIC1',
        active: true,
        timestamp: Date
    }
}

// Changements de scène
{
    type: 'scene_changed',
    data: {
        sceneName: 'Scene 2',
        previousScene: 'Scene 1',
        timestamp: Date
    }
}

// Changements de visibilité
{
    type: 'source_visibility_changed',
    data: {
        sourceName: 'Camera1',
        sceneName: 'Scene 2',
        visible: true,
        timestamp: Date
    }
}
```

## 🔗 Créer des automations

### Exemple 1 : Automation basée sur le volume

Créez un fichier de lien personnalisé pour réagir aux changements de volume :

```typescript
// src/core/links/volume-automation.link.ts
export class VolumeAutomationLink implements IModuleLink {
    private setupOBSListeners(): void {
        this.obsModule.onEvent((event) => {
            if (event.type === 'volume_changed') {
                const { sourceName, volumeDb } = event.data;
                
                // Si le volume dépasse -6dB, envoyer une alerte
                if (volumeDb > -6) {
                    console.warn(`⚠️ Volume élevé détecté sur ${sourceName}: ${volumeDb}dB`);
                    // Ici vous pourriez envoyer une notification, etc.
                }
            }
        });
    }
}
```

### Exemple 2 : Automation de caméra basée sur la scène

```typescript
// Changer automatiquement de preset caméra selon la scène OBS
export class SceneCameraAutomationLink implements IModuleLink {
    private setupOBSListeners(): void {
        this.obsModule.onEvent((event) => {
            if (event.type === 'scene_changed') {
                const { sceneName } = event.data;
                
                // Mapper les scènes OBS aux presets caméra
                const cameraPresets = {
                    'Scene Interview': 'preset_interview',
                    'Scene Presentation': 'preset_wide',
                    'Scene Close-up': 'preset_closeup'
                };
                
                const preset = cameraPresets[sceneName];
                if (preset) {
                    // Déclencher le changement de preset caméra
                    this.triggerCameraPreset(preset);
                }
            }
        });
    }
}
```

### Exemple 3 : Automation basée sur la visibilité des sources

```typescript
// Réagir quand certaines sources deviennent visibles
export class SourceVisibilityAutomationLink implements IModuleLink {
    private setupOBSListeners(): void {
        this.obsModule.onEvent((event) => {
            if (event.type === 'source_visibility_changed') {
                const { sourceName, visible } = event.data;
                
                // Si la source "Lower Third" devient visible
                if (sourceName === 'Lower Third' && visible) {
                    // Déclencher une animation ou notification
                    this.triggerLowerThirdAnimation();
                }
                
                // Si une caméra devient visible, ajuster l'éclairage
                if (sourceName.startsWith('Camera') && visible) {
                    this.adjustLightingForCamera(sourceName);
                }
            }
        });
    }
}
```

## 🚀 Utilisation pratique

### 1. Consulter l'état actuel

Vous pouvez consulter l'état actuel via l'API `/status` :

```bash
curl http://localhost:3000/status
```

Réponse :
```json
{
    "modules": {
        "obs": {
            "connection": { "connected": true },
            "currentScene": "Scene Interview",
            "source_MIC1_volume": { "db": -12.5, "mul": 0.25 },
            "source_MIC1_muted": false,
            "scene_Scene Interview_source_Camera1_enabled": true
        }
    }
}
```

### 2. Créer des liens personnalisés

1. Créez un fichier dans `src/core/links/`
2. Implémentez l'interface `IModuleLink`
3. Ajoutez-le dans le gestionnaire de liens
4. Configurez-le dans la configuration des liens

### 3. Intégrer avec d'autres systèmes

Les événements OBS peuvent déclencher des actions vers :
- **Gabin** : Changements de caméra automatiques
- **Companion** : Feedbacks visuels sur les surfaces de contrôle
- **Systèmes externes** : API REST, WebHooks, etc.

## 📝 Bonnes pratiques

### Performance
- Les états sont mis à jour en temps réel sans polling
- Les événements sont émis uniquement lors de changements réels
- L'état est stocké en mémoire pour un accès rapide

### Fiabilité
- Reconnexion automatique en cas de perte de connexion OBS
- Gestion d'erreurs robuste
- Logs détaillés pour le débogage

### Extensibilité
- Architecture modulaire pour ajouter facilement de nouveaux types d'observation
- Événements standardisés pour l'interopérabilité
- Configuration flexible des liens d'automation

## 🔍 Débogage

### Logs utiles
```
[obs] Scene changed: Scene 1 → Scene 2
[obs] Source visibility changed in current scene: Camera1 = true
[obs] Volume adjusted for 'MIC1': -12.5dB
```

### Vérification des états
Utilisez l'endpoint `/status` pour vérifier que les états sont correctement observés.

### Test des automations
Créez des liens de test simples qui loggent les événements pour vérifier que vos automations reçoivent bien les données attendues.
