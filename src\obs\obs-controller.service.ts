/**
 * Service de contrôle OBS
 * Responsabilité : Envoyer des commandes vers OBS
 */

import { Injectable } from '@nestjs/common';
import { OBSWebSocket } from 'obs-websocket-js';

@Injectable()
export class OBSControllerService {
    constructor() {}

    /**
     * Changer de scène OBS
     * @param obs Instance OBS WebSocket connectée
     * @param sceneName Nom de la scène à activer
     */
    async changeScene(obs: OBSWebSocket, sceneName: string): Promise<void> {
        if (!obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await obs.call('SetCurrentProgramScene', { sceneName });
            console.log(`[obs-controller] Scene changed to: ${sceneName}`);
        } catch (error: any) {
            console.error(`[obs-controller] Failed to change scene to "${sceneName}":`, error.message);
            throw error;
        }
    }

    /**
     * Contrôler la visibilité d'une source dans une scène
     * @param obs Instance OBS WebSocket connectée
     * @param sceneName Nom de la scène
     * @param sourceName Nom de la source
     * @param visible True pour rendre visible, false pour masquer
     */
    async setSourceVisibility(obs: OBSWebSocket, sceneName: string, sourceName: string, visible: boolean): Promise<void> {
        if (!obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await obs.call('SetSceneItemEnabled', {
                sceneName,
                sceneItemId: await this.getSceneItemId(obs, sceneName, sourceName),
                sceneItemEnabled: visible,
            });
            console.log(`[obs-controller] Source "${sourceName}" in scene "${sceneName}" set to ${visible ? 'visible' : 'hidden'}`);
        } catch (error: any) {
            console.error(`[obs-controller] Failed to set visibility of "${sourceName}" in "${sceneName}":`, String(error?.message || 'Unknown error'));
            throw error;
        }
    }

    /**
     * Masquer toutes les sources spécifiées dans une scène
     * @param obs Instance OBS WebSocket connectée
     * @param sceneName Nom de la scène
     * @param sourceNames Liste des noms de sources à masquer
     */
    async hideAllSources(obs: OBSWebSocket, sceneName: string, sourceNames: string[]): Promise<void> {
        if (!obs.identified) {
            throw new Error('OBS not connected');
        }

        const promises = sourceNames.map((sourceName) =>
            this.setSourceVisibility(obs, sceneName, sourceName, false).catch((error) => {
                console.warn(`[obs-controller] Failed to hide source "${sourceName}":`, String(error?.message || 'Unknown error'));
            }),
        );

        await Promise.allSettled(promises);
        console.log(`[obs-controller] Attempted to hide ${sourceNames.length} sources in scene "${sceneName}"`);
    }

    /**
     * Obtenir l'ID d'un élément de scène
     * @param obs Instance OBS WebSocket connectée
     * @param sceneName Nom de la scène
     * @param sourceName Nom de la source
     * @returns ID de l'élément de scène
     */
    private async getSceneItemId(obs: OBSWebSocket, sceneName: string, sourceName: string): Promise<number> {
        try {
            const response = await obs.call('GetSceneItemId', {
                sceneName,
                sourceName,
            });
            return response.sceneItemId;
        } catch (error: any) {
            console.error(`[obs-controller] Failed to get scene item ID for "${sourceName}" in "${sceneName}":`, String(error?.message || 'Unknown error'));
            throw error;
        }
    }

    // Ici vous pourrez ajouter d'autres méthodes de contrôle si nécessaire :
    // - setSourceVolume()
    // - setSourceMute()
    // - etc.
}
