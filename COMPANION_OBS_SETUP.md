# 🎛️ Configuration Companion pour contrôler OBS

Ce guide explique comment configurer Bitfocus Companion pour contrôler le volume et le mute des sources audio OBS via OBS Video Hub.

## 📋 Prérequis

1. **OBS Video Hub** configuré et en fonctionnement
2. **OBS Studio** connecté via WebSocket
3. **Bitfocus Companion** installé et configuré
4. Sources audio configurées dans OBS (micros, etc.)

## ⚙️ Configuration OBS Video Hub

### 1. Activer les modules nécessaires

Dans `src/config/local/modules.local.ts` :
```typescript
export const modulesLocalConfig: Partial<ModulesConfig> = {
    obs: {
        enabled: true,  // ✅ Obligatoire
    },
    companion: {
        enabled: true,  // ✅ Obligatoire
    }
};
```

### 2. Configurer les feedbacks Companion

Dans `src/config/local/companion.local.ts`, décommentez et configurez :
```typescript
feedbacks: [
    {
        type: 'mic',
        path: '/mic/MIC1',
        micName: 'MIC1',  // ⚠️ Nom EXACT de votre source audio OBS
    },
    {
        type: 'mic',
        path: '/mic/MIC2',
        micName: 'MIC2',  // ⚠️ Nom EXACT de votre source audio OBS
    },
    // Ajoutez toutes vos sources audio OBS ici
],
```

**Important** : Les noms `micName` doivent correspondre EXACTEMENT aux noms de vos sources audio dans OBS.

## 🎛️ Configuration Companion

### 1. Configuration OSC

Dans Companion, configurez la connexion OSC :
- **Type** : OSC
- **Target IP** : `127.0.0.1` (si Hub sur même machine)
- **Target Port** : `33224` (port par défaut Hub)
- **Listen Port** : `33223` (port par défaut Hub)

### 2. Actions disponibles

#### 🔊 Contrôle du volume

| Action OSC | Description | Paramètres |
|------------|-------------|------------|
| `/action/volume/SOURCE_NAME/set` | Volume absolu | Valeur en dB (ex: -12.5) |
| `/action/volume/SOURCE_NAME/adjust` | Volume relatif | Pourcentage (ex: +10, -5) |

#### 🔇 Contrôle mute/unmute

| Action OSC | Description |
|------------|-------------|
| `/action/volume/SOURCE_NAME/mute` | Couper le son |
| `/action/volume/SOURCE_NAME/unmute` | Rétablir le son |
| `/action/mic/SOURCE_NAME/toggle` | Basculer mute/unmute |
| `/action/mic/SOURCE_NAME/on` | Activer (unmute) |
| `/action/mic/SOURCE_NAME/off` | Désactiver (mute) |

### 3. Exemples de boutons Companion

#### Bouton Volume +10%
- **Action** : OSC
- **Path** : `/action/volume/MIC1/adjust`
- **Arguments** : `10`

#### Bouton Volume -10%
- **Action** : OSC  
- **Path** : `/action/volume/MIC1/adjust`
- **Arguments** : `-10`

#### Bouton Volume fixe -12dB
- **Action** : OSC
- **Path** : `/action/volume/MIC1/set`
- **Arguments** : `-12`

#### Bouton Mute/Unmute
- **Action** : OSC
- **Path** : `/action/mic/MIC1/toggle`
- **Feedback** : OSC
- **Path** : `/mic/MIC1`

## 🔄 Feedbacks visuels

Les feedbacks sont automatiquement configurés :
- **État connecté** : Bouton vert (valeur 1)
- **État déconnecté** : Bouton rouge (valeur 0)  
- **État indéterminé** : Bouton gris (valeur 2)

## 🧪 Test de la configuration

### 1. Vérifier la connexion

1. Démarrez OBS Video Hub
2. Vérifiez les logs : `[obs-to-companion] Link initialized successfully`
3. Vérifiez les logs : `[companion-to-obs] Link initialized successfully`

### 2. Tester les actions

1. Dans Companion, créez un bouton avec `/action/volume/MIC1/adjust` et argument `10`
2. Appuyez sur le bouton
3. Vérifiez dans OBS que le volume de la source MIC1 a augmenté
4. Vérifiez les logs Hub : `[companion-to-obs] Volume adjusted for 'MIC1': 10%`

### 3. Tester les feedbacks

1. Dans OBS, changez manuellement le mute d'une source
2. Le bouton Companion correspondant doit changer d'état
3. Vérifiez les logs : `[obs-to-companion] Mic feedback sent: MIC1 = true`

## 🐛 Dépannage

### Problème : Pas de réaction aux actions
- ✅ Vérifiez que les modules OBS et Companion sont activés
- ✅ Vérifiez que les liens sont activés dans les logs
- ✅ Vérifiez les noms de sources dans la configuration

### Problème : Pas de feedbacks
- ✅ Vérifiez la configuration des feedbacks dans `companion.local.ts`
- ✅ Vérifiez que le lien `obs-to-companion` est activé
- ✅ Vérifiez les noms de sources correspondent exactement à OBS

### Problème : Erreurs de connexion
- ✅ Vérifiez les ports OSC (33223/33224 par défaut)
- ✅ Vérifiez que OBS WebSocket est activé
- ✅ Vérifiez les logs de connexion des modules

## 📝 Notes importantes

1. **Noms de sources** : Doivent être identiques entre OBS et la configuration Hub
2. **Ports OSC** : Par défaut 33223 (écoute) et 33224 (envoi)
3. **Valeurs volume** : 
   - Ajustement relatif : -100 à +100 (pourcentage)
   - Volume absolu : valeurs en dB (ex: -12.5, 0, +6)
4. **Redémarrage** : Redémarrez Hub après modification de la configuration
