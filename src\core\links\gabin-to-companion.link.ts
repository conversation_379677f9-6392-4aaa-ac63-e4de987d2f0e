/**
 * <PERSON><PERSON> → Companion
 * Synchronise les états de Gabin vers Companion pour les feedbacks
 */

import { IModuleLink, LinkConfig } from '../interfaces/link.interface';
import { IModule, IMicrophoneModule, IAutocamModule, IControlModule, MODULE_EVENTS } from '../interfaces/module.interface';

export class GabinToCompanionLink implements IModuleLink {
    public readonly name = 'gabin-to-companion';
    public readonly description = 'Synchronise les états Gabin vers Companion pour les feedbacks';
    public readonly enabled: boolean;

    private gabinModule?: IMicrophoneModule & IAutocamModule;
    private companionModule?: IControlModule;
    private cleanupCallbacks: Array<() => void> = [];

    constructor(private config: LinkConfig) {
        this.enabled = config.enabled;
    }

    async initialize(modules: Map<string, IModule>): Promise<void> {
        if (!this.enabled) {
            return;
        }

        // Récupérer les modules nécessaires
        this.gabinModule = modules.get('gabin') as IMicrophoneModule & IAutocamModule;
        this.companionModule = modules.get('companion') as IControlModule;

        if (!this.gabinModule) {
            console.log(`[${this.name}] Gabin module not available - link disabled`);
            return;
        }

        if (!this.companionModule) {
            console.log(`[${this.name}] Companion module not available - link disabled`);
            return;
        }

        console.log(`[${this.name}] Initializing link between Gabin and Companion`);

        // S'abonner aux événements de Gabin
        this.setupGabinListeners();

        // Envoyer l'état initial
        await this.sendInitialStates();

        console.log(`[${this.name}] Link initialized successfully`);
    }

    async cleanup(): Promise<void> {
        console.log(`[${this.name}] Cleaning up link`);

        // Nettoyer tous les listeners
        this.cleanupCallbacks.forEach((cleanup) => cleanup());
        this.cleanupCallbacks = [];

        this.gabinModule = undefined;
        this.companionModule = undefined;
    }

    /**
     * Configurer les listeners sur les événements Gabin
     */
    private setupGabinListeners(): void {
        if (!this.gabinModule || !this.companionModule) return;

        // Écouter les changements d'état de connexion Gabin
        const connectionListener = (event: any) => {
            if (event.type === MODULE_EVENTS.CONNECTION_CHANGED) {
                this.handleGabinConnectionChange(event.data.connected);
            }
        };

        // Écouter les changements d'autocam
        const autocamListener = (event: any) => {
            if (event.type === MODULE_EVENTS.AUTOCAM_STATE_CHANGED && this.config.options?.syncAutocam) {
                this.handleAutocamChange(event.data.active);
            }
        };

        // Écouter les changements de micros
        const micListener = (event: any) => {
            if (event.type === MODULE_EVENTS.MIC_STATE_CHANGED && this.config.options?.syncMics) {
                this.handleMicChange(event.data.micName, event.data.active);
            }
        };

        this.gabinModule.onEvent(connectionListener);
        this.gabinModule.onEvent(autocamListener);
        this.gabinModule.onEvent(micListener);

        // Stocker les callbacks pour le nettoyage
        this.cleanupCallbacks.push(() => console.log(`[${this.name}] Cleaned up Gabin listeners`));
    }

    /**
     * Envoyer les états initiaux vers Companion
     */
    private async sendInitialStates(): Promise<void> {
        if (!this.gabinModule || !this.companionModule) return;

        try {
            // Envoyer l'état de l'autocam si activé
            if (this.config.options?.syncAutocam) {
                const autocamState = this.gabinModule.getAutocamState();
                if (autocamState !== null) {
                    await this.companionModule.sendFeedback?.('autocam', autocamState);
                }
            }

            // Envoyer l'état des micros si activé
            if (this.config.options?.syncMics) {
                const micStates = this.gabinModule.getAllMicStates();
                for (const [micName, active] of Object.entries(micStates)) {
                    await this.companionModule.sendFeedback?.('mic', active, micName);
                }
            }

            console.log(`[${this.name}] Initial states sent to Companion`);
        } catch (error) {
            console.error(`[${this.name}] Error sending initial states:`, error.message);
        }
    }

    /**
     * Gérer les changements de connexion Gabin
     */
    private async handleGabinConnectionChange(connected: boolean): Promise<void> {
        if (!this.companionModule) return;

        try {
            if (connected) {
                console.log(`[${this.name}] Gabin connected - sending current states`);
                await this.sendInitialStates();
            } else {
                console.log(`[${this.name}] Gabin disconnected - sending disconnected states`);
                await this.sendDisconnectedStates();
            }
        } catch (error) {
            console.error(`[${this.name}] Error handling Gabin connection change:`, error.message);
        }
    }

    /**
     * Gérer les changements d'autocam
     */
    private async handleAutocamChange(active: boolean): Promise<void> {
        if (!this.companionModule) return;

        try {
            await this.companionModule.sendFeedback?.('autocam', active);
            console.log(`[${this.name}] Autocam state sent to Companion: ${active}`);
        } catch (error) {
            console.error(`[${this.name}] Error sending autocam state:`, error.message);
        }
    }

    /**
     * Gérer les changements de micros
     */
    private async handleMicChange(micName: string, active: boolean): Promise<void> {
        if (!this.companionModule) return;

        try {
            await this.companionModule.sendFeedback?.('mic', active, micName);
            console.log(`[${this.name}] Mic state sent to Companion: ${micName} = ${active}`);
        } catch (error) {
            console.error(`[${this.name}] Error sending mic state:`, error.message);
        }
    }

    /**
     * Envoyer des états "déconnectés" vers Companion
     */
    private async sendDisconnectedStates(): Promise<void> {
        if (!this.companionModule) return;

        try {
            // Envoyer des états "undefined" pour griser les boutons
            await this.companionModule.sendFeedback?.('autocam', undefined);

            // Pour les micros, on pourrait avoir une liste configurée
            const knownMics = ['MIC1', 'MIC2']; // TODO: Récupérer depuis la config
            for (const micName of knownMics) {
                await this.companionModule.sendFeedback?.('mic', undefined, micName);
            }

            console.log(`[${this.name}] Disconnected states sent to Companion`);
        } catch (error) {
            console.error(`[${this.name}] Error sending disconnected states:`, error.message);
        }
    }
}
