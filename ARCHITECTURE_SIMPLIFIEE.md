# 🎯 Architecture OBS Simplifiée

## 📁 Structure des fichiers

```
src/obs/
├── obs-module.service.ts      # Service principal (connexion, orchestration)
├── obs-observer.service.ts    # Écoute des événements OBS (ultra-simple)
├── obs-controller.service.ts  # Contrôles vers OBS (changeScene uniquement)
└── obs.types.ts              # Types TypeScript
```

## 🔍 Observer Service - Ultra-simplifié

Le service observer ne fait que **3 choses** :

### 1. Écouter les événements OBS
```typescript
obs.on('InputMuteStateChanged', (data) => {
    this.handleInputMuteChanged(data.inputName, data.inputMuted);
});

obs.on('InputVolumeChanged', (data) => {
    this.handleInputVolumeChanged(data.inputName, data.inputVolumeDb, data.inputVolumeMul);
});

obs.on('CurrentProgramSceneChanged', (data) => {
    this.handleSceneChanged(data.sceneName);
});
```

### 2. Formater et émettre les événements
```typescript
private handleInputMuteChanged(inputName: string, inputMuted: boolean): void {
    this.emitEvent('input_mute_changed', {
        inputName,
        inputMuted,
    });
}

private handleInputVolumeChanged(inputName: string, inputVolumeDb: number, inputVolumeMul: number): void {
    this.emitEvent('input_volume_changed', {
        inputName,
        inputVolumeDb,
        inputVolumeMul,
    });
}

private handleSceneChanged(sceneName: string): void {
    this.emitEvent('scene_changed', {
        sceneName,
    });
}
```

### 3. Transmettre via callback
```typescript
private emitEvent(type: string, data: any): void {
    if (this.eventCallback) {
        this.eventCallback(type, data);
    }
}
```

## 🎮 Controller Service - Minimal

Le service controller ne contient qu'**un exemple** :

```typescript
async changeScene(obs: OBSWebSocket, sceneName: string): Promise<void> {
    if (!obs.identified) {
        throw new Error('OBS not connected');
    }

    try {
        await obs.call('SetCurrentProgramScene', { sceneName });
        console.log(`[obs-controller] Scene changed to: ${sceneName}`);
    } catch (error: any) {
        console.error(`[obs-controller] Failed to change scene to "${sceneName}":`, error.message);
        throw error;
    }
}
```

## 🔗 Service Principal - Orchestration

Le service principal :
- Gère la connexion WebSocket
- Configure les services observer et controller
- Transmet les événements vers le Hub

```typescript
private setupServices(): void {
    // Configurer le callback pour les événements de l'observer
    this.observer.setEventCallback((type: string, data: any) => {
        // Émettre l'événement vers les autres modules
        this.emitEvent(type, data);
    });

    // Configurer les listeners de connexion OBS
    this.setupConnectionListeners();
}
```

## 🚀 Utilisation

### Écouter les événements OBS

Dans un lien ou autre service :

```typescript
// S'abonner aux événements du module OBS
obsModule.onEvent((event) => {
    switch (event.type) {
        case 'input_mute_changed':
            console.log(`Micro ${event.data.inputName} ${event.data.inputMuted ? 'coupé' : 'activé'}`);
            break;
            
        case 'input_volume_changed':
            console.log(`Volume ${event.data.inputName}: ${event.data.inputVolumeDb}dB`);
            break;
            
        case 'scene_changed':
            console.log(`Scène changée: ${event.data.sceneName}`);
            break;
    }
});
```

### Contrôler OBS

```typescript
// Changer de scène
await obsModule.changeScene('Scene Interview');
```

## ✨ Avantages de cette architecture

1. **Ultra-simple** : Chaque service a une responsabilité claire
2. **Extensible** : Facile d'ajouter de nouveaux événements ou contrôles
3. **Découplé** : Observer et Controller sont indépendants
4. **Événementiel** : Tout fonctionne via des événements
5. **Minimal** : Pas de stockage d'état complexe, pas de getters multiples

## 🔧 Ajouter un nouvel événement

### 1. Dans l'observer
```typescript
// Ajouter le listener
obs.on('NouvelEvenementOBS', (data) => {
    this.handleNouvelEvenement(data.param1, data.param2);
});

// Ajouter le handler
private handleNouvelEvenement(param1: string, param2: number): void {
    this.emitEvent('nouvel_evenement', {
        param1,
        param2,
    });
}
```

### 2. Écouter l'événement
```typescript
obsModule.onEvent((event) => {
    if (event.type === 'nouvel_evenement') {
        console.log('Nouvel événement:', event.data);
    }
});
```

## 🎯 Ajouter un nouveau contrôle

### 1. Dans le controller
```typescript
async nouvelleAction(obs: OBSWebSocket, param: string): Promise<void> {
    if (!obs.identified) {
        throw new Error('OBS not connected');
    }

    try {
        await obs.call('CommandeOBS', { param });
        console.log(`[obs-controller] Action effectuée: ${param}`);
    } catch (error: any) {
        console.error(`[obs-controller] Erreur:`, error.message);
        throw error;
    }
}
```

### 2. Dans le service principal
```typescript
async nouvelleAction(param: string): Promise<void> {
    if (!this.isReady()) {
        throw new Error('OBS not connected');
    }

    try {
        await this.controller.nouvelleAction(this.obs, param);
    } catch (error: any) {
        this.handleError(error, `Failed to execute nouvelle action`);
        throw error;
    }
}
```

Cette architecture est maintenant **ultra-simple** et **facilement extensible** ! 🎉
