/**
 * Export centralisé de toutes les configurations
 * Merge automatique des configurations par défaut + locales
 */

import { gabinDefaultConfig } from './defaults/gabin.default';
import { companionDefaultConfig } from './defaults/companion.default';
import { obsDefaultConfig } from './defaults/obs.default';
import { modulesDefaultConfig } from './defaults/modules.default';
import { audioToCameraDefaultConfig } from './defaults/audio-to-camera.default';
import type { GabinConfig } from '../gabin/gabin.types';
import type { CompanionConfig } from '../companion/companion.types';
import type { OBSConfig } from '../obs/obs.types';
import type { ModulesConfig } from './defaults/modules.default';
import type { AudioToCameraConfig } from './defaults/audio-to-camera.default';

// Import conditionnel des configurations locales avec gestion d'erreur propre
// Note: Utilisation de require() car les fichiers locaux peuvent ne pas exister
// et les imports ES6 dynamiques nécessiteraient une refactorisation complète
function loadGabinLocalConfig(): Partial<GabinConfig> {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const localModule = require('./local/gabin.local') as { gabinLocalConfig?: Partial<GabinConfig> };
        return localModule.gabinLocalConfig || {};
    } catch {
        // Fichier local n'existe pas ou erreur d'import - retourner config vide
        return {};
    }
}

function loadCompanionLocalConfig(): Partial<CompanionConfig> {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const localModule = require('./local/companion.local') as { companionLocalConfig?: Partial<CompanionConfig> };
        return localModule.companionLocalConfig || {};
    } catch {
        // Fichier local n'existe pas ou erreur d'import - retourner config vide
        return {};
    }
}

function loadOBSLocalConfig(): Partial<OBSConfig> {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const localModule = require('./local/obs.local') as { obsLocalConfig?: Partial<OBSConfig> };
        return localModule.obsLocalConfig || {};
    } catch {
        // Fichier local n'existe pas ou erreur d'import - retourner config vide
        return {};
    }
}

function loadModulesLocalConfig(): Partial<ModulesConfig> {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const localModule = require('./local/modules.local') as { modulesLocalConfig?: Partial<ModulesConfig> };
        return localModule.modulesLocalConfig || {};
    } catch {
        // Fichier local n'existe pas ou erreur d'import - retourner config vide
        return {};
    }
}

function loadAudioToCameraLocalConfig(): Partial<AudioToCameraConfig> {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const localModule = require('./local/audio-to-camera.local') as { audioToCameraLocalConfig?: Partial<AudioToCameraConfig> };
        return localModule.audioToCameraLocalConfig || {};
    } catch {
        // Fichier local n'existe pas ou erreur d'import - retourner config vide
        return {};
    }
}

// Chargement des configurations locales
const gabinLocalConfig = loadGabinLocalConfig();
const companionLocalConfig = loadCompanionLocalConfig();
const obsLocalConfig = loadOBSLocalConfig();
const modulesLocalConfig = loadModulesLocalConfig();
const audioToCameraLocalConfig = loadAudioToCameraLocalConfig();

/**
 * Merge profond de deux objets de configuration
 */
function deepMerge<T>(defaults: T, overrides: Partial<T>): T {
    const result = { ...defaults };

    for (const key in overrides) {
        if (overrides[key] !== undefined) {
            if (typeof overrides[key] === 'object' && !Array.isArray(overrides[key]) && overrides[key] !== null) {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
                result[key] = deepMerge(result[key] as any, overrides[key] as any);
            } else {
                result[key] = overrides[key] as any;
            }
        }
    }

    return result;
}

// Merge des configurations
export const gabinConfig = deepMerge(gabinDefaultConfig, gabinLocalConfig);
export const companionConfig = deepMerge(companionDefaultConfig, companionLocalConfig);
export const obsConfig = deepMerge(obsDefaultConfig, obsLocalConfig);
export const modulesConfig = deepMerge(modulesDefaultConfig, modulesLocalConfig);
export const audioToCameraConfig = deepMerge(audioToCameraDefaultConfig, audioToCameraLocalConfig);

// Export de la configuration app (inchangée)
export { appConfig, type AppConfig, type HttpConfig } from './app.config';
