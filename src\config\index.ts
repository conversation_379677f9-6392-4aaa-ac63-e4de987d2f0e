/**
 * Export centralisé de toutes les configurations
 * Chargement direct des configurations locales
 */

import type { GabinConfig } from '../gabin/gabin.types';
import type { CompanionConfig } from '../companion/companion.types';
import type { OBSConfig } from '../obs/obs.types';

// Types pour les configurations qui n'ont pas de module dédié
export interface ModulesConfig {
    gabin: {
        enabled: boolean;
        description: string;
    };
    obs: {
        enabled: boolean;
        description: string;
    };
    companion: {
        enabled: boolean;
        description: string;
    };
}

export interface MicToCameraMapping {
    micName: string;
    cameraSource: string;
}

export interface AudioToCameraConfig {
    enabled: boolean;
    targetScene: string;
    audioThresholdDb: number;
    activationDelayMs: number;
    holdDurationMs: number;
    minChangeIntervalMs: number;
    micToCameraMapping: MicToCameraMapping[];
    fallbackBehavior: {
        hideAllWhenInactive: boolean;
        hideDelayMs: number;
    };
    fullscreenMode: {
        enabled: boolean;
        multiMicDurationMs: number;
        minActiveMics: number;
    };
    debug: {
        verbose: boolean;
        logAudioLevels: boolean;
    };
}

// Chargement direct des configurations locales
function loadGabinLocalConfig(): GabinConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const localModule = require('./local/gabin.local') as { gabinLocalConfig: GabinConfig };
        return localModule.gabinLocalConfig;
    } catch (error) {
        throw new Error(`Failed to load gabin.local.ts: ${error}`);
    }
}

function loadCompanionLocalConfig(): CompanionConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const localModule = require('./local/companion.local') as { companionLocalConfig: CompanionConfig };
        return localModule.companionLocalConfig;
    } catch (error) {
        throw new Error(`Failed to load companion.local.ts: ${error}`);
    }
}

function loadOBSLocalConfig(): OBSConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const localModule = require('./local/obs.local') as { obsLocalConfig: OBSConfig };
        return localModule.obsLocalConfig;
    } catch (error) {
        throw new Error(`Failed to load obs.local.ts: ${error}`);
    }
}

function loadModulesLocalConfig(): ModulesConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const localModule = require('./local/modules.local') as { modulesLocalConfig: ModulesConfig };
        return localModule.modulesLocalConfig;
    } catch (error) {
        throw new Error(`Failed to load modules.local.ts: ${error}`);
    }
}

function loadAudioToCameraLocalConfig(): AudioToCameraConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const localModule = require('./local/audio-to-camera.local') as { audioToCameraLocalConfig: AudioToCameraConfig };
        return localModule.audioToCameraLocalConfig;
    } catch (error) {
        throw new Error(`Failed to load audio-to-camera.local.ts: ${error}`);
    }
}

// Export des configurations locales (chargement différé)
let _gabinConfig: GabinConfig | undefined;
let _companionConfig: CompanionConfig | undefined;
let _obsConfig: OBSConfig | undefined;
let _modulesConfig: ModulesConfig | undefined;
let _audioToCameraConfig: AudioToCameraConfig | undefined;

export const gabinConfig = (): GabinConfig => {
    if (!_gabinConfig) {
        _gabinConfig = loadGabinLocalConfig();
    }
    return _gabinConfig;
};

export const companionConfig = (): CompanionConfig => {
    if (!_companionConfig) {
        _companionConfig = loadCompanionLocalConfig();
    }
    return _companionConfig;
};

export const obsConfig = (): OBSConfig => {
    if (!_obsConfig) {
        _obsConfig = loadOBSLocalConfig();
    }
    return _obsConfig;
};

export const modulesConfig = (): ModulesConfig => {
    if (!_modulesConfig) {
        _modulesConfig = loadModulesLocalConfig();
    }
    return _modulesConfig;
};

export const audioToCameraConfig = (): AudioToCameraConfig => {
    if (!_audioToCameraConfig) {
        _audioToCameraConfig = loadAudioToCameraLocalConfig();
    }
    return _audioToCameraConfig;
};

// Export de la configuration app (inchangée)
export { appConfig, type AppConfig, type HttpConfig } from './app.config';
