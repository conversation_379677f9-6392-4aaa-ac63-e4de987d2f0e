import * as fs from 'fs';
import * as path from 'path';

/**
 * Générateur de fichiers de configuration locaux
 * Auto-génère les fichiers *.local.ts s'ils n'existent pas
 */
export class ConfigGenerator {
    private static readonly LOCAL_DIR = (() => {
        // Détecter si nous sommes en mode dev (src/) ou build (dist/)
        const currentDir = __dirname;
        if (currentDir.includes('dist')) {
            // Mode production : fichiers dans src/config/local depuis dist/config
            return path.join(__dirname, '..', '..', 'src', 'config', 'local');
        } else {
            // Mode développement : fichiers dans le même répertoire
            return path.join(__dirname, 'local');
        }
    })();

    /**
     * Génère tous les fichiers de configuration locaux manquants
     * Retourne true si des fichiers ont été générés (première fois)
     */
    static generateMissingLocalConfigs(): boolean {
        this.ensureLocalDirExists();

        const gabinGenerated = this.generateGabinLocalConfig();
        const companionGenerated = this.generateCompanionLocalConfig();
        const obsGenerated = this.generateOBSLocalConfig();
        const modulesGenerated = this.generateModulesLocalConfig();
        const audioToCameraGenerated = this.generateAudioToCameraLocalConfig();

        const isFirstTime = gabinGenerated || companionGenerated || obsGenerated || modulesGenerated || audioToCameraGenerated;

        if (isFirstTime) {
            this.showFirstTimeMessageAndExit();
        }

        return isFirstTime;
    }

    /**
     * S'assure que le dossier local/ existe
     */
    private static ensureLocalDirExists(): void {
        if (!fs.existsSync(this.LOCAL_DIR)) {
            fs.mkdirSync(this.LOCAL_DIR, { recursive: true });
            console.log('[Config] Created local config directory');
        }
    }

    /**
     * Génère gabin.local.ts s'il n'existe pas
     * Retourne true si le fichier a été généré
     */
    private static generateGabinLocalConfig(): boolean {
        const filePath = path.join(this.LOCAL_DIR, 'gabin.local.ts');

        if (!fs.existsSync(filePath)) {
            const content = this.getGabinLocalTemplate();
            fs.writeFileSync(filePath, content, 'utf8');
            console.log('[Config] Generated gabin.local.ts');
            return true;
        }
        return false;
    }

    /**
     * Génère companion.local.ts s'il n'existe pas
     * Retourne true si le fichier a été généré
     */
    private static generateCompanionLocalConfig(): boolean {
        const filePath = path.join(this.LOCAL_DIR, 'companion.local.ts');

        if (!fs.existsSync(filePath)) {
            const content = this.getCompanionLocalTemplate();
            fs.writeFileSync(filePath, content, 'utf8');
            console.log('[Config] Generated companion.local.ts');
            return true;
        }
        return false;
    }

    /**
     * Génère obs.local.ts s'il n'existe pas
     * Retourne true si le fichier a été généré
     */
    private static generateOBSLocalConfig(): boolean {
        const filePath = path.join(this.LOCAL_DIR, 'obs.local.ts');

        if (!fs.existsSync(filePath)) {
            const content = this.getOBSLocalTemplate();
            fs.writeFileSync(filePath, content, 'utf8');
            console.log('[Config] Generated obs.local.ts');
            return true;
        }
        return false;
    }

    /**
     * Génère modules.local.ts s'il n'existe pas
     * Retourne true si le fichier a été généré
     */
    private static generateModulesLocalConfig(): boolean {
        const filePath = path.join(this.LOCAL_DIR, 'modules.local.ts');

        if (!fs.existsSync(filePath)) {
            const content = this.getModulesLocalTemplate();
            fs.writeFileSync(filePath, content, 'utf8');
            console.log('[Config] Generated modules.local.ts');
            return true;
        }
        return false;
    }

    /**
     * Génère audio-to-camera.local.ts s'il n'existe pas
     * Retourne true si le fichier a été généré
     */
    private static generateAudioToCameraLocalConfig(): boolean {
        const filePath = path.join(this.LOCAL_DIR, 'audio-to-camera.local.ts');

        if (!fs.existsSync(filePath)) {
            const content = this.getAudioToCameraLocalTemplate();
            fs.writeFileSync(filePath, content, 'utf8');
            console.log('[Config] Generated audio-to-camera.local.ts');
            return true;
        }
        return false;
    }

    /**
     * Affiche le message de première configuration et arrête l'application immédiatement
     */
    private static showFirstTimeMessageAndExit(): void {
        console.log('\n' + '='.repeat(80));
        console.log('🎉 CONFIGURATION INITIALE TERMINÉE');
        console.log('='.repeat(80));
        console.log('');
        console.log('📁 Les fichiers de configuration locaux ont été générés :');
        console.log('   • src/config/local/gabin.local.ts');
        console.log('   • src/config/local/companion.local.ts');
        console.log('   • src/config/local/obs.local.ts');
        console.log('   • src/config/local/modules.local.ts');
        console.log('   • src/config/local/audio-to-camera.local.ts');
        console.log('');
        console.log('🔧 PROCHAINES ÉTAPES :');
        console.log('   1. Personnalisez vos configurations dans les fichiers locaux');
        console.log('   2. Activez les modules souhaités dans modules.local.ts');
        console.log("   3. Redémarrez l'application : npm run start");
        console.log('');
        console.log('💡 CONSEILS :');
        console.log('   • Les fichiers locaux sont ignorés par Git');
        console.log('   • Modifiez directement les valeurs selon vos besoins');
        console.log("   • Consultez le README.md pour plus d'informations");
        console.log('');
        console.log('='.repeat(80));
        console.log('');
        console.log('🛑 Application arrêtée pour configuration. Redémarrez après personnalisation.');

        // Arrêt immédiat sans délai
        process.exit(0);
    }

    /**
     * Template pour gabin.local.ts
     */
    private static getGabinLocalTemplate(): string {
        return `import { GabinConfig } from '../../gabin/gabin.types';

export const gabinLocalConfig: GabinConfig = {
    network: {
        listenPort: 33123,
        sendPort: 32123,
        sendHost: '127.0.0.1',
    },
    timing: {
        pingInterval: 5000,
        pingTimeout: 3000,
        reconnectionDelay: 500,
    },
    registrations: [
        { type: 'autocam', path: '/autocam' },
        { type: 'micFeedback', path: '/mic/MIC1' },
        { type: 'micFeedback', path: '/mic/MIC2' },
    ],
};
`;
    }

    /**
     * Template pour companion.local.ts
     */
    private static getCompanionLocalTemplate(): string {
        return `import { CompanionConfig } from '../../companion/companion.types';

export const companionLocalConfig: CompanionConfig = {
    network: {
        listenPort: 33223,
        host: '127.0.0.1',
        port: 33224,
    },
    timing: {
        connectionCheckInterval: 6000,
        initialStateDelay: 1000,
    },
    feedbacks: [
        {
            type: 'autocam',
            path: '/autocam',
        },
        {
            type: 'mic',
            path: '/mic/MIC1',
            micName: 'MIC1',
        },
        {
            type: 'mic',
            path: '/mic/MIC2',
            micName: 'MIC2',
        },
    ],
};
`;
    }

    /**
     * Template pour obs.local.ts
     */
    private static getOBSLocalTemplate(): string {
        return `import { OBSConfig } from '../../obs/obs.types';

export const obsLocalConfig: OBSConfig = {
    network: {
        host: '127.0.0.1',
        port: 4455,
        password: undefined,
    },
    timing: {
        connectionTimeout: 5000,
        reconnectInterval: 10000,
        pingInterval: 30000,
    },
    autoConnect: true,
};
`;
    }

    /**
     * Template pour modules.local.ts
     */
    private static getModulesLocalTemplate(): string {
        return `export interface ModulesConfig {
    gabin: {
        enabled: boolean;
        description: string;
    };
    obs: {
        enabled: boolean;
        description: string;
    };
    companion: {
        enabled: boolean;
        description: string;
    };
}

export const modulesLocalConfig: ModulesConfig = {
    gabin: {
        enabled: false,
        description: 'Module Gabin pour caméras automatiques et gestion des micros',
    },
    obs: {
        enabled: false,
        description: 'Module OBS Studio pour contrôle via WebSocket',
    },
    companion: {
        enabled: false,
        description: 'Module Companion pour surfaces de contrôle OSC',
    },
};
`;
    }

    /**
     * Template pour audio-to-camera.local.ts
     */
    private static getAudioToCameraLocalTemplate(): string {
        return `export interface MicToCameraMapping {
    micName: string;
    cameraSource: string;
}

export interface AudioToCameraConfig {
    enabled: boolean;
    targetScene: string;
    audioThresholdDb: number;
    activationDelayMs: number;
    holdDurationMs: number;
    minChangeIntervalMs: number;
    micToCameraMapping: MicToCameraMapping[];
    fallbackBehavior: {
        hideAllWhenInactive: boolean;
        hideDelayMs: number;
    };
    fullscreenMode: {
        enabled: boolean;
        multiMicDurationMs: number;
        minActiveMics: number;
    };
    debug: {
        verbose: boolean;
        logAudioLevels: boolean;
    };
}

export const audioToCameraLocalConfig: AudioToCameraConfig = {
    enabled: false,
    targetScene: 'AUTO CAM',
    audioThresholdDb: -40,
    activationDelayMs: 100,
    holdDurationMs: 4000,
    minChangeIntervalMs: 3000,
    micToCameraMapping: [
        { micName: 'mic_invite1', cameraSource: 'CAM 1' },
        { micName: 'mic_invite2', cameraSource: 'CAM 2' },
        { micName: 'mic_invite3', cameraSource: 'CAM 3' },
    ],
    fallbackBehavior: {
        hideAllWhenInactive: true,
        hideDelayMs: 3000,
    },
    fullscreenMode: {
        enabled: true,
        multiMicDurationMs: 3000,
        minActiveMics: 2,
    },
    debug: {
        verbose: false,
        logAudioLevels: false,
    },
};
`;
    }
}
